import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { TrackingKeys } from '../services/EventTrackingFactory';

class FilterField extends FilterFieldBase<TrackingKeys> {}

export class EventIdFilter {
  private static readonly field: TrackingKeys = 'eventId';

  static buildEqual(id: string): Filter {
    const idField = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(id);

    return new Filter(idField, operator, filterValue);
  }
}
