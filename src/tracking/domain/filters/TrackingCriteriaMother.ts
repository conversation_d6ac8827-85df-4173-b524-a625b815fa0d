import {
  Criteria,
  Filters,
  Order,
} from '@discocil/fv-criteria-converter-library/domain';
import { UniqueEntityID } from '@discocil/fv-domain-library/domain';

import { EventIdFilter } from '@/cross-cutting/domain/filters/EventIdFilter';
import { OrganizationIdFilter } from '@/cross-cutting/domain/filters/OrganizationIdFilter';

import type { SearchMetricsDto } from '../contracts/SearchMetricsDto';
import type { TrackingKeys } from '../services/EventTrackingFactory';

export class TrackingCriteriaMother {
  static readonly defaultOrderKey: TrackingKeys = 'createdAt';

  static metricsToMatch(dto: SearchMetricsDto): Criteria {
    const filters = Filters.build();

    const eventId = UniqueEntityID.build(dto.eventId);

    filters.add(EventIdFilter.buildEqual(eventId));

    if (dto.organizationId) {
      const organizationId = UniqueEntityID.build(dto.organizationId);

      filters.add(OrganizationIdFilter.buildEqual(organizationId));
    }

    return Criteria.build(
      filters,
      Order.asc<TrackingKeys>(
        this.defaultOrderKey,
      ),
    );
  }
}
