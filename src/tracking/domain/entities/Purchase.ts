import {
  Collection,
  CreatedAt,
  CreatedBy,
  Fv<PERSON><PERSON>ber,
  left,
  Money,
  RemovedAt,
  RemovedBy,
  right,
  UnexpectedError,
  UniqueEntityID,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

import { TrackedPurchaseDomainEvent } from '../events/TrackedPurchaseDomainEvent';
import { EventType } from '../value-objects/EventType';

import { EventTracking } from './EventTracking';

import type {
  Either,
  EMicrositeServices,
  InvalidArgumentError,
  Maybe,
  MoneyError,
  MoneyProps,
  Primitives,
} from '@discocil/fv-domain-library/domain';
import type {
  EMicrositeContainerType,
  TrackingContent,
  TrackingContentPrimitives,
  TrackingFb,
  TrackingItem,
  TrackingItemPrimitive,
  TrackingUser,
} from '../contracts/EntityContracts';

export type PurchasePrimitives = Primitives<Purchase>;

type PurchaseEither = Either<InvalidArgumentError | UnexpectedError | MoneyError, Purchase>;

type PurchaseContent = Omit<TrackingContent, 'date'>;
export type PurchaseContentPrimitives = Primitives<Omit<TrackingContentPrimitives, 'date'>>;

export class Purchase extends EventTracking {
  private constructor(
    id: UniqueEntityID,
    externalId: Maybe<UniqueEntityID>,
    organizationId: UniqueEntityID,
    urlPage: URL,
    user: Maybe<TrackingUser>,
    fb: Maybe<TrackingFb>,
    remoteAddress: string,
    userAgent: string,
    type: EventType,
    private readonly _price: Money,
    private readonly _items: Collection<TrackingItem>,
    private readonly _numItems: FvNumber,
    private readonly _totalPrice: Money,
    private readonly _content: PurchaseContent,
    eventId: Maybe<UniqueEntityID>,
    sessionId: Maybe<string>,
    serviceType: Maybe<EMicrositeServices>,
    containerType: Maybe<EMicrositeContainerType>,
    createdAt: Maybe<CreatedAt>,
    createdBy: Maybe<CreatedBy>,
    updatedAt: Maybe<UpdatedAt>,
    updatedBy: Maybe<UpdatedBy>,
    removedAt: Maybe<RemovedAt>,
    removedBy: Maybe<RemovedBy>,
  ) {
    super(
      id,
      type,
      externalId,
      organizationId,
      urlPage,
      user,
      fb,
      remoteAddress,
      userAgent,
      eventId,
      sessionId,
      serviceType,
      containerType,
      createdAt,
      createdBy,
      updatedAt,
      updatedBy,
      removedAt,
      removedBy,
    );
  }

  static build(primitives: PurchasePrimitives): PurchaseEither {
    const priceOrError = Money.build(primitives.price);

    if (priceOrError.isLeft()) {
      return left(priceOrError.value);
    }

    const totalPriceOrError = Money.build(primitives.totalPrice);

    if (totalPriceOrError.isLeft()) {
      return left(totalPriceOrError.value);
    }

    const price = priceOrError.value;
    const totalPrice = totalPriceOrError.value;

    try {
      const id = UniqueEntityID.build(primitives.id);
      const externalId = primitives.externalId.map(item => UniqueEntityID.build(item));
      const organizationId = UniqueEntityID.build(primitives.organizationId);

      const urlPage = new URL(primitives.urlPage);
      const numItems = FvNumber.build(primitives.numItems);

      const user = primitives.user.map(item => item);
      const fb = primitives.fb.map(item => item);

      const content = {
        id: UniqueEntityID.build(primitives.content.id),
        name: primitives.content.name,
        type: primitives.content.type,
        ids: Collection.new<string>('id'),
      };

      const items = Collection.new<TrackingItem>('id');

      for (const primitiveItem of primitives.items) {
        const priceOrError = Money.build({
          amount: primitiveItem.price,
          currency: primitives.price.currency,
        });

        if (priceOrError.isLeft()) {
          return left(priceOrError.value);
        }

        const id = UniqueEntityID.build(primitiveItem.id);
        const price = priceOrError.value;
        const quantity = FvNumber.build(primitiveItem.quantity);

        items.add({
          ...primitiveItem,
          id,
          price,
          quantity,
        });
      }

      const entity = new Purchase(
        id,
        externalId,
        organizationId,
        urlPage,
        user,
        fb,
        primitives.remoteAddress,
        primitives.userAgent,
        EventType.Purchase(),
        price,
        items,
        numItems,
        totalPrice,
        content,
        primitives.eventId.map(item => UniqueEntityID.build(item)),
        primitives.sessionId.map(item => item),
        primitives.serviceType.map(item => item),
        primitives.containerType.map(item => item),
        primitives.createdAt.map(item => CreatedAt.build(item)),
        primitives.createdBy.map(item => CreatedBy.build(item)),
        primitives.updatedAt.map(item => UpdatedAt.build(item)),
        primitives.updatedBy.map(item => UpdatedBy.build(item)),
        primitives.removedAt.map(item => RemovedAt.build(item)),
        primitives.removedBy.map(item => RemovedBy.build(item)),
      );

      entity.addDomainEvent(
        TrackedPurchaseDomainEvent.build({
          aggregateId: entity._id,
          attributes: entity.toPrimitives(),
        }),
      );

      return right(entity);
    } catch (error) {
      const parsedError = error as Error;

      return left(UnexpectedError.build({
        context: Purchase.name,
        error: parsedError,
        data: primitives,
      }));
    }
  }

  get price(): MoneyProps {
    return this._price.toPrimitive();
  }

  get items(): TrackingItemPrimitive[] {
    return this._items.toArray().map(item => ({
      ...item,
      id: item.id.toPrimitive(),
      price: item.price.toDecimal(),
      quantity: item.quantity.toPrimitive(),
    }));
  }

  get numItems(): number {
    return this._numItems.toPrimitive();
  }

  get totalPrice(): MoneyProps {
    return this._totalPrice.toPrimitive();
  }

  get content(): PurchaseContentPrimitives {
    return {
      ...this._content,
      id: this._content.id.toPrimitive(),
      ids: this._content.ids.toArray(),
    };
  }

  toPrimitives(): PurchasePrimitives {
    return {
      id: this.id,
      name: this.name,
      externalId: this.externalId,
      organizationId: this.organizationId,
      urlPage: this.urlPage,
      user: this.user,
      fb: this.fb,
      price: this.price,
      items: this.items,
      numItems: this.numItems,
      totalPrice: this.totalPrice,
      content: this.content,
      remoteAddress: this.remoteAddress,
      userAgent: this.userAgent,
      eventId: this.eventId,
      sessionId: this.sessionId,
      serviceType: this.serviceType,
      containerType: this.containerType,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this.updatedAt,
      updatedBy: this.updatedBy,
      removedAt: this.removedAt,
      removedBy: this.removedBy,
    };
  }
}
