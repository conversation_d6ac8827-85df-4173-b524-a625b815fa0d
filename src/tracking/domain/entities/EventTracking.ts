import { AggregateRoot, Maybe } from '@discocil/fv-domain-library/domain';


import type { MaybePrimitiveStamps } from '@/cross-cutting/domain/services/Stamps';
import type {
  CreatedAt,
  CreatedBy,
  Either,
  EMicrositeServices, IdPrimitive, RemovedAt, RemovedBy, UnexpectedError, UniqueEntityID,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';
import type {
  EMicrositeContainerType, TrackingFb, TrackingUser,
} from '../contracts/EntityContracts';
import type { EEventTypes, EventType } from '../value-objects/EventType';

export type EventTrackings = Map<IdPrimitive, EventTracking>;

export type TrackingMetricsFvEventResponse = {
  readonly allEventTrackings: EventTrackings;
  readonly eventTrackingsForTheLast15Minutes: EventTrackings;
};

export type TrackingMetricsFvEventEither = Either<UnexpectedError, TrackingMetricsFvEventResponse>;

export abstract class EventTracking extends AggregateRoot {
  protected constructor(
    id: UniqueEntityID,
    private readonly _type: EventType,
    private readonly _externalId: Maybe<UniqueEntityID>,
    private readonly _organizationId: UniqueEntityID,
    private readonly _urlPage: URL,
    readonly user: Maybe<TrackingUser>,
    readonly fb: Maybe<TrackingFb>,
    readonly remoteAddress: string,
    readonly userAgent: string,
    private readonly _eventId: Maybe<UniqueEntityID>,
    private readonly _sessionId: Maybe<string>,
    private readonly _serviceType: Maybe<EMicrositeServices>,
    private readonly _containerType: Maybe<EMicrositeContainerType>,
    private readonly _createdAt: Maybe<CreatedAt>,
    private readonly _createdBy: Maybe<CreatedBy>,
    private readonly _updatedAt: Maybe<UpdatedAt>,
    private readonly _updatedBy: Maybe<UpdatedBy>,
    private readonly _removedAt: Maybe<RemovedAt>,
    private readonly _removedBy: Maybe<RemovedBy>,
  ) {
    super(id);
  }

  get name(): EEventTypes {
    return this._type.toPrimitive();
  }

  get externalId(): Maybe<string> {
    return this._externalId.map(item => item.toPrimitive());
  }

  get organizationId(): string {
    return this._organizationId.toPrimitive();
  }

  get urlPage(): string {
    return this._urlPage.toString();
  }

  get eventId(): Maybe<string> {
    return this._eventId.map(item => item.toPrimitive());
  }

  get sessionId(): Maybe<string> {
    return this._sessionId;
  }

  get serviceType(): Maybe<EMicrositeServices> {
    return this._serviceType;
  }

  get containerType(): Maybe<EMicrositeContainerType> {
    return this._containerType;
  }

  isPageView(): boolean {
    return this._type.isPageView();
  }

  get createdAt(): Maybe<number> {
    return this._createdAt.map(item => item.toMilliseconds());
  }

  get createdBy(): Maybe<string> {
    return this._createdBy.map(item => item.toPrimitive());
  }

  get updatedAt(): Maybe<number> {
    return this._updatedAt.map(item => item.toMilliseconds());
  }

  get updatedBy(): Maybe<string> {
    return this._updatedBy.map(item => item.toPrimitive());
  }

  get removedAt(): Maybe<number> {
    return this._removedAt.map(item => item.toMilliseconds());
  }

  get removedBy(): Maybe<string> {
    return this._removedBy.map(item => item.toPrimitive());
  }

  static makeDefaultStamps(): MaybePrimitiveStamps {
    return {
      createdAt: Maybe.none<number>(),
      createdBy: Maybe.none<string>(),
      updatedAt: Maybe.none<number>(),
      updatedBy: Maybe.none<string>(),
      removedAt: Maybe.none<number>(),
      removedBy: Maybe.none<string>(),
    };
  }
}
