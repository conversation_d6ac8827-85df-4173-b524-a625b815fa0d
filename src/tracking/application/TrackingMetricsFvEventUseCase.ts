import { type PaginationOption, OrderTypes } from '@discocil/fv-criteria-converter-library/domain';
import {
  FvDate,
  left,
  Maybe,
  NotFoundError,
  right,
  UnexpectedError,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import { EventEntity } from '@/events/events/domain/entities/EventEntity';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';

import { SearchMetricsDto } from '../domain/contracts/SearchMetricsDto';
import { TrackingSearchRepository } from '../domain/contracts/TrackingMongoRepository';
import { EventTrackings, TrackingMetricsFvEventEither } from '../domain/entities/EventTracking';
import { TrackingCriteriaMother } from '../domain/filters/TrackingCriteriaMother';
import {
  EventMetaFactory, SearchPaginatedTrackings, TrackingsEither,
} from '../domain/services/EventTrackingFactory';

import type { FetchAllPages } from '@/cross-cutting/domain/contracts/FetchAllPages';
import type { TrackingEventDto } from '@/tracking/domain/contracts/TrackingEventUseCaseContracts';
import type { UseCase } from '@discocil/fv-domain-library/application';

export class TrackingMetricsFvEventUseCase implements UseCase<TrackingEventDto, Promise<TrackingMetricsFvEventEither>> {
  constructor(
    private readonly trackingRepository: TrackingSearchRepository,
    private readonly eventRepository: EventRepository,
    private readonly fetchAllPages: FetchAllPages,
  ) {}

  @contextualizeError()
  async execute(dto: TrackingEventDto): Promise<TrackingMetricsFvEventEither> {
    const eventId = dto.eventId;

    if (eventId.isEmpty()) {
      return left(NotFoundError.build({
        context: this.constructor.name,
        target: EventEntity.name,
      }).notAutoContextualizable());
    }

    const eventCriteria = EventCriteriaMother.idToMatch(
      UniqueEntityID.build(eventId.get()),
    );

    const eventOrError = await this.eventRepository.find(eventCriteria);

    if (eventOrError.isLeft()) {
      return left(eventOrError.value);
    }

    const eventFactoryOrError = EventMetaFactory.execute(dto);

    if (eventFactoryOrError.isLeft()) {
      return left(eventFactoryOrError.value);
    }

    const searchEventTicketTypesPagination: PaginationOption = {
      page: 1,
      perPage: 400,
      order: {
        type: OrderTypes.ASC,
        by: Maybe.some(TrackingCriteriaMother.defaultOrderKey),
      },
    };

    const searchEventTicketTypesDto: SearchMetricsDto = {
      eventId: eventId.get(),
      organizationId: dto.organizationId,
      pagination: searchEventTicketTypesPagination,
    };

    const allTrackingEventsResponseOrError = await this.fetchAllPages.execute<
      SearchPaginatedTrackings,
      TrackingsEither,
      UnexpectedError
    >(
      searchEventTicketTypesPagination,
      (pagination) => {
        const criteria = TrackingCriteriaMother.metricsToMatch({
          ...searchEventTicketTypesDto,
          pagination,
        });

        return this.trackingRepository.search(criteria);
      },
    );

    if (allTrackingEventsResponseOrError.isLeft()) {
      return left(allTrackingEventsResponseOrError.value);
    }

    const allTrackingEventsResponses = allTrackingEventsResponseOrError.value;

    const last15MinutesTreshold = FvDate.create().subtractMinutes(15);

    const allEventTrackings: EventTrackings = new Map();
    const eventTrackingsForTheLast15Minutes: EventTrackings = new Map();

    for (const _eventTracking of allTrackingEventsResponses) {
      const { trackings } = _eventTracking;

      for (const _tracking of trackings.values()) {
        allEventTrackings.set(_tracking.id, _tracking);

        if (_tracking.createdAt.isDefined()) {
          const createdAtDate = FvDate.createFromMilliSeconds(_tracking.createdAt.get());

          if (createdAtDate.isGreaterThan(last15MinutesTreshold)) {
            eventTrackingsForTheLast15Minutes.set(_tracking.id, _tracking);
          }
        }
      }
    }

    return right({
      allEventTrackings,
      eventTrackingsForTheLast15Minutes,
    });
  }
}
