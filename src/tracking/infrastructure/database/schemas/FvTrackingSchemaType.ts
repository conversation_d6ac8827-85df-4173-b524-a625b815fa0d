import type { EMicrositeContainerType } from '@/tracking/domain/contracts/EntityContracts';
import type { EEventTypes } from '@/tracking/domain/value-objects/EventType';
import type { EMicrositeServices, IdPrimitive } from '@discocil/fv-domain-library';

export type FvTrackingSchemaType = {
  _id: IdPrimitive;
  name: EEventTypes;
  event_id: IdPrimitive;
  organization_id: IdPrimitive;
  url_page: string;
  price: number | null;
  remote_address: string;
  user_agent: string;
  session_id: string;
  service_type: EMicrositeServices;
  container_type: EMicrositeContainerType;
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  removed_at: number;
  removed_by: string;
};
