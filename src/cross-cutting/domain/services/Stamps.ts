import {
  CreatedAt,
  CreatedBy,
  FvDate,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

import type { Maybe } from '@discocil/fv-domain-library/domain';

export type Stamps = {
  createdAt: CreatedAt;
  createdBy: CreatedBy;
  updatedAt: UpdatedAt;
  updatedBy: UpdatedBy;
  removedAt: RemovedAt;
  removedBy: RemovedBy;
};

export type MaybePrimitiveStamps = {
  createdAt: Maybe<number>;
  createdBy: Maybe<string>;
  updatedAt: Maybe<number>;
  updatedBy: Maybe<string>;
  removedAt: Maybe<number>;
  removedBy: Maybe<string>;
};

export type StampsPrimitive = {
  createdAt: number;
  createdBy: string;
  updatedAt: number;
  updatedBy: string;
  removedAt: number;
  removedBy: string;
};

export type StampsJsonPrimitives = {
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
  removedAt: string;
  removedBy: string;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type ValueObjects = Record<string, any>;

type LocalPrimitives = Record<string, unknown>;

export const defaultStamps = (): StampsPrimitive => {
  return {
    createdAt: FvDate.create().toMilliseconds(),
    updatedAt: FvDate.create().toMilliseconds(),
    removedAt: 0,
    createdBy: '',
    updatedBy: '',
    removedBy: '',
  };
};

export const stampValueObjects = (primitives: LocalPrimitives): Stamps => {
  const valueObjects: Partial<Stamps> = {};

  const match: ValueObjects = {
    createdAt: CreatedAt,
    createdBy: CreatedBy,
    updatedAt: UpdatedAt,
    updatedBy: UpdatedBy,
    removedAt: RemovedAt,
    removedBy: RemovedBy,
  };

  for (const propertyName in match) {
    const propName = propertyName as keyof Stamps;
    const valueObject = match[propertyName].build(primitives[propName]);

    valueObjects[propName] = valueObject;
  }

  return valueObjects as Stamps;
};

export const entityStampsToJson = (entity: StampsPrimitive): StampsJsonPrimitives => {
  return {
    createdAt: FvDate.createFromMilliSeconds(entity.createdAt).toISO(),
    updatedAt: FvDate.createFromMilliSeconds(entity.updatedAt).toISO(),
    removedAt: FvDate.createFromMilliSeconds(entity.removedAt).toISO(),
    createdBy: entity.createdBy,
    updatedBy: entity.updatedBy,
    removedBy: entity.removedBy,
  };
};

export const entityStampsFromJson = (json: StampsJsonPrimitives): StampsPrimitive => {
  const createdAtOrError = FvDate.createFromISO(json.createdAt);

  if (createdAtOrError.isLeft()) {
    throw new Error(`Invalid createdAt: ${json.createdAt}`);
  }

  const updatedAtOrError = FvDate.createFromISO(json.updatedAt);

  if (updatedAtOrError.isLeft()) {
    throw new Error(`Invalid updatedAt: ${json.updatedAt}`);
  }

  const removedAtOrError = FvDate.createFromISO(json.removedAt);

  if (removedAtOrError.isLeft()) {
    throw new Error(`Invalid removedAt: ${json.removedAt}`);
  }

  return {
    createdAt: createdAtOrError.value.toMilliseconds(),
    updatedAt: updatedAtOrError.value.toMilliseconds(),
    removedAt: removedAtOrError.value.toMilliseconds(),
    createdBy: json.createdBy,
    updatedBy: json.updatedBy,
    removedBy: json.removedBy,
  };
};
