import { Type } from '@sinclair/typebox';

import { EEventChannel } from '@/tracking/domain/value-objects/EventType';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const paramsSchema = Type.Object({ channel: Type.Enum(EEventChannel) });

const querySchema = Type.Object({
  smsSale: Type.Optional(Type.Boolean({ default: false })),
  slugChannel: Type.Optional(Type.String()),
});

const errorResponseSchema = Type.Object({ message: Type.String() });

const successResponseSchema = Type.Object({});

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const trackingMetricsSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};

type Schema = typeof trackingMetricsSchema;

export type TrackingMetricsRequest = FastifyRequestTypebox<Schema>;

export type TrackingMetricsReply = FastifyReplyTypebox<Schema>;

// !TODO
// export type TrackingMetricsResponse = ReturnType<ReturnType<typeof Response>['execute']>;


