import { singleton } from 'tsyringe';

import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { TrackingMetricsFvEventUseCase } from '@/tracking/application/TrackingMetricsFvEventUseCase';
import { TrackingMetricsReply, TrackingMetricsRequest } from '@app/http/@types/cli-api/tracking/metrics/schema';
import { HTTP_CODES } from '@app/http/HttpCodes';

@singleton()
export class TrackingMetricsController {
  constructor(
    private readonly useCase: TrackingMetricsFvEventUseCase,
  ) { }

  async handler(request: TrackingMetricsRequest, reply: TrackingMetricsReply): Promise<void> {
    const useCaseResult = await this.useCase.execute();

    if (useCaseResult.isLeft()) {
      request.error = useCaseResult.value.contextualize({ context: this.constructor.name });

      const { code, message } = getErrorInfo(request.error);

      return reply.code(code).send({ message });
    }

    return reply.code(HTTP_CODES.NO_CONTENT_204).send();
  }
}
